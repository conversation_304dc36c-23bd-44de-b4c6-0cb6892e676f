# Berlix UI

**Motion-first. Hassle-free. Fully yours.**

Berlix UI is a hand-crafted collection of animated React components powered by **Tailwind CSS** and **Motion** — designed to look smooth, feel snappy, and drop into your app with zero friction.

Built on top of **shadcn/ui**, you can install components using the `shadcn-ui` CLI or simply copy-paste from the docs. Every component is:

- 🌀 **Minimal & unstyled** – fits any design system
- 🛠️ **Fully customizable** – via props or direct code access
- ⚡ **Motion-enhanced** – animations everywhere
- 🔁 **Flexible usage** – install or copy-paste

> No themes. No constraints. Just animated components that get out of your way.

---

**Perfect for production apps, prototypes, or design systems.**  
Use Berlix UI wherever you want clean motion and full control — without the bloat.
