# Syntax Highlighting Test

This page tests the syntax highlighting functionality across different programming languages.

## JavaScript

```javascript
function greetUser(name) {
  const message = `Hello, ${name}!`;
  console.log(message);
  
  // Array methods
  const numbers = [1, 2, 3, 4, 5];
  const doubled = numbers.map(n => n * 2);
  
  return {
    message,
    doubled,
    timestamp: new Date().toISOString()
  };
}

// Async/await example
async function fetchUserData(userId) {
  try {
    const response = await fetch(`/api/users/${userId}`);
    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Failed to fetch user data:', error);
    throw error;
  }
}
```

## TypeScript

```typescript
interface User {
  id: number;
  name: string;
  email: string;
  isActive: boolean;
}

type UserRole = 'admin' | 'user' | 'moderator';

class UserService {
  private users: User[] = [];

  constructor(private apiUrl: string) {}

  async createUser(userData: Omit<User, 'id'>): Promise<User> {
    const newUser: User = {
      id: Date.now(),
      ...userData
    };
    
    this.users.push(newUser);
    return newUser;
  }

  findUsersByRole<T extends UserRole>(role: T): User[] {
    return this.users.filter(user => user.role === role);
  }
}

// Generic utility type
type ApiResponse<T> = {
  data: T;
  status: 'success' | 'error';
  message?: string;
};
```

## React/JSX

```jsx
import React, { useState, useEffect } from 'react';

const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch user');
        }
        
        const userData = await response.json();
        setUser(userData);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) return <div className="spinner">Loading...</div>;
  if (error) return <div className="error">Error: {error}</div>;
  if (!user) return <div>User not found</div>;

  return (
    <div className="user-profile">
      <h1>{user.name}</h1>
      <p>Email: {user.email}</p>
      <button 
        onClick={() => setUser(prev => ({ ...prev, isActive: !prev.isActive }))}
        className={`btn ${user.isActive ? 'btn-success' : 'btn-secondary'}`}
      >
        {user.isActive ? 'Deactivate' : 'Activate'}
      </button>
    </div>
  );
};

export default UserProfile;
```

## React/TSX

```tsx
import React, { useState, useCallback } from 'react';

interface TodoItem {
  id: string;
  text: string;
  completed: boolean;
  createdAt: Date;
}

interface TodoListProps {
  initialTodos?: TodoItem[];
  onTodoChange?: (todos: TodoItem[]) => void;
}

const TodoList: React.FC<TodoListProps> = ({ 
  initialTodos = [], 
  onTodoChange 
}) => {
  const [todos, setTodos] = useState<TodoItem[]>(initialTodos);
  const [newTodoText, setNewTodoText] = useState<string>('');

  const addTodo = useCallback(() => {
    if (!newTodoText.trim()) return;

    const newTodo: TodoItem = {
      id: crypto.randomUUID(),
      text: newTodoText.trim(),
      completed: false,
      createdAt: new Date()
    };

    const updatedTodos = [...todos, newTodo];
    setTodos(updatedTodos);
    setNewTodoText('');
    onTodoChange?.(updatedTodos);
  }, [newTodoText, todos, onTodoChange]);

  const toggleTodo = useCallback((id: string) => {
    const updatedTodos = todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    );
    setTodos(updatedTodos);
    onTodoChange?.(updatedTodos);
  }, [todos, onTodoChange]);

  return (
    <div className="todo-list">
      <div className="add-todo">
        <input
          type="text"
          value={newTodoText}
          onChange={(e) => setNewTodoText(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addTodo()}
          placeholder="Add a new todo..."
        />
        <button onClick={addTodo}>Add</button>
      </div>
      
      <ul className="todos">
        {todos.map(todo => (
          <li key={todo.id} className={todo.completed ? 'completed' : ''}>
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
            />
            <span>{todo.text}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default TodoList;
```

## Python

```python
from typing import List, Dict, Optional
import asyncio
import aiohttp
from dataclasses import dataclass
from datetime import datetime

@dataclass
class User:
    id: int
    name: str
    email: str
    created_at: datetime
    is_active: bool = True

class UserRepository:
    def __init__(self, db_url: str):
        self.db_url = db_url
        self.users: List[User] = []
    
    async def create_user(self, name: str, email: str) -> User:
        """Create a new user and save to database."""
        user = User(
            id=len(self.users) + 1,
            name=name,
            email=email,
            created_at=datetime.now()
        )
        
        self.users.append(user)
        await self._save_to_db(user)
        return user
    
    async def find_users_by_status(self, is_active: bool) -> List[User]:
        """Find all users by their active status."""
        return [user for user in self.users if user.is_active == is_active]
    
    async def _save_to_db(self, user: User) -> None:
        """Private method to save user to database."""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.db_url}/users",
                json={
                    "name": user.name,
                    "email": user.email,
                    "created_at": user.created_at.isoformat()
                }
            ) as response:
                if response.status != 201:
                    raise Exception(f"Failed to save user: {response.status}")

# Usage example
async def main():
    repo = UserRepository("https://api.example.com")
    
    # Create users
    users = await asyncio.gather(
        repo.create_user("Alice", "<EMAIL>"),
        repo.create_user("Bob", "<EMAIL>"),
        repo.create_user("Charlie", "<EMAIL>")
    )
    
    # Find active users
    active_users = await repo.find_users_by_status(True)
    print(f"Found {len(active_users)} active users")

if __name__ == "__main__":
    asyncio.run(main())
```

## CSS

```css
/* Modern CSS with custom properties and grid */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #64748b;
  --background: #ffffff;
  --text-color: #1f2937;
  --border-radius: 8px;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #111827;
    --text-color: #f9fafb;
    --secondary-color: #9ca3af;
  }
}

.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
  background: var(--background);
  color: var(--text-color);
}

.card {
  background: var(--background);
  border: 1px solid var(--secondary-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.card__title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.card__content {
  line-height: 1.6;
  color: var(--secondary-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .card {
    padding: 1rem;
  }
}

/* Animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeInUp 0.6s ease-out;
}
```

## Bash/Shell

```bash
#!/bin/bash

# Script to deploy a web application
set -euo pipefail

# Configuration
APP_NAME="my-web-app"
DEPLOY_DIR="/var/www/${APP_NAME}"
BACKUP_DIR="/var/backups/${APP_NAME}"
LOG_FILE="/var/log/${APP_NAME}-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    error "This script should not be run as root"
fi

# Create backup
create_backup() {
    log "Creating backup..."
    
    if [[ -d "$DEPLOY_DIR" ]]; then
        local backup_name="${APP_NAME}-$(date +%Y%m%d-%H%M%S).tar.gz"
        tar -czf "${BACKUP_DIR}/${backup_name}" -C "$DEPLOY_DIR" .
        log "Backup created: ${backup_name}"
    else
        warning "Deploy directory does not exist, skipping backup"
    fi
}

# Deploy application
deploy_app() {
    log "Starting deployment..."
    
    # Build the application
    npm run build || error "Build failed"
    
    # Stop the service
    sudo systemctl stop "$APP_NAME" || warning "Failed to stop service"
    
    # Copy files
    rsync -av --delete dist/ "$DEPLOY_DIR/" || error "Failed to copy files"
    
    # Set permissions
    sudo chown -R www-data:www-data "$DEPLOY_DIR"
    sudo chmod -R 755 "$DEPLOY_DIR"
    
    # Start the service
    sudo systemctl start "$APP_NAME" || error "Failed to start service"
    sudo systemctl enable "$APP_NAME"
    
    log "Deployment completed successfully!"
}

# Health check
health_check() {
    log "Performing health check..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "http://localhost:3000/health" > /dev/null; then
            log "Health check passed!"
            return 0
        fi
        
        log "Health check attempt $attempt/$max_attempts failed, retrying..."
        sleep 2
        ((attempt++))
    done
    
    error "Health check failed after $max_attempts attempts"
}

# Main execution
main() {
    log "Starting deployment process for $APP_NAME"
    
    create_backup
    deploy_app
    health_check
    
    log "Deployment process completed successfully!"
}

# Run main function
main "$@"
```

## JSON

```json
{
  "name": "my-awesome-app",
  "version": "1.0.0",
  "description": "An awesome web application",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "build": "webpack --mode production",
    "test": "jest",
    "lint": "eslint src/",
    "format": "prettier --write src/"
  },
  "dependencies": {
    "express": "^4.18.2",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.4.0",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "@types/node": "^20.4.0",
    "@types/react": "^18.2.0",
    "eslint": "^8.44.0",
    "jest": "^29.6.0",
    "nodemon": "^3.0.0",
    "prettier": "^3.0.0",
    "typescript": "^5.1.0",
    "webpack": "^5.88.0"
  },
  "keywords": [
    "react",
    "typescript",
    "web",
    "application"
  ],
  "author": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "url": "https://johndoe.dev"
  },
  "license": "MIT",
  "repository": {
    "type": "git",
    "url": "https://github.com/johndoe/my-awesome-app.git"
  },
  "bugs": {
    "url": "https://github.com/johndoe/my-awesome-app/issues"
  },
  "homepage": "https://my-awesome-app.com"
}
```

This test page demonstrates syntax highlighting across multiple programming languages with proper color coding, keyword highlighting, and theme support.
