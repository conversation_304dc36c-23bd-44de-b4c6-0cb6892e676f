{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "tabs", "title": "tabs", "description": "A minimalistic tab component designed with React and Tailwind CSS.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "tabs.tsx", "content": "'use client';\n\nimport { useState } from 'react';\n\nexport type TabItem = {\n  id: string | number;\n  name: string;\n  content: string;\n};\n\nexport interface TabsProps {\n  items?: TabItem[];\n}\n\nexport default function Tabs({ items = [] }: TabsProps) {\n  const [activeTab, setActiveTab] = useState<string | number>(items[0]?.id ?? 1);\n\n  return (\n    <div className='bg-white dark:bg-gray-900 rounded-xl shadow-lg border border-gray-200 dark:border-gray-800 overflow-hidden w-full max-w-2xl'>\n      {/* Tabs Header */}\n      <div className='flex border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-950'>\n        {items.map((tab) => (\n          <button\n            key={tab.id}\n            onClick={() => setActiveTab(tab.id)}\n            className={`relative px-5 py-3 text-sm font-medium transition-all duration-300 ease-in-out transform hover:scale-105 ${\n              activeTab === tab.id\n                ? 'text-blue-600 dark:text-blue-400'\n                : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'\n            }`}\n            aria-selected={activeTab === tab.id}\n            role='tab'\n          >\n            <span>{tab.name}</span>\n            {activeTab === tab.id && (\n              <span className='absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 dark:bg-blue-400 transition-transform duration-300 ease-in-out transform origin-left'></span>\n            )}\n          </button>\n        ))}\n      </div>\n\n      {/* Content Area */}\n      <div\n        role='tabpanel'\n        className='p-6 bg-white dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-opacity duration-300 ease-in-out'\n      >\n        {items.find((tab) => tab.id === activeTab)?.content || items[0]?.content}\n      </div>\n    </div>\n  );\n}\n", "type": "registry:ui"}]}